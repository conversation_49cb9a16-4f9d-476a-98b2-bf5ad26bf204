import { useState, useCallback } from "react";
import {
  aiTrainingService,
  SearchResult,
  TrainingDataItem,
} from "../services/aiTrainingService";

interface UseAITrainingState {
  isLoading: boolean;
  error: string | null;
  searchResults: SearchResult[];
  aiResponse: string | null;
  systemStatus: Record<string, unknown> | null;
  trainingJob: Record<string, unknown> | null;
}

export const useAITraining = () => {
  const [state, setState] = useState<UseAITrainingState>({
    isLoading: false,
    error: null,
    searchResults: [],
    aiResponse: null,
    systemStatus: null,
    trainingJob: null,
  });

  const setLoading = (isLoading: boolean) => {
    setState((prev) => ({ ...prev, isLoading }));
  };

  const setError = (error: string | null) => {
    setState((prev) => ({ ...prev, error }));
  };

  /**
   * Perform vector search
   */
  const performVectorSearch = useCallback(
    async (
      query: string,
      collection: string = "clients",
      limit: number = 10
    ) => {
      setLoading(true);
      setError(null);

      try {
        const results = await aiTrainingService.performVectorSearch({
          query,
          collection,
          limit,
        });

        setState((prev) => ({
          ...prev,
          searchResults: results,
          isLoading: false,
        }));

        return results;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        setError(errorMessage);
        setLoading(false);
        throw error;
      }
    },
    []
  );

  /**
   * Generate AI response
   */
  const generateAIResponse = useCallback(
    async (
      userQuery: string,
      searchResults?: SearchResult[],
      useRAG: boolean = true
    ) => {
      setLoading(true);
      setError(null);

      try {
        const response = await aiTrainingService.generateAIResponse({
          userQuery,
          searchResults,
          useRAG,
        });

        setState((prev) => ({
          ...prev,
          aiResponse: response.response,
          isLoading: false,
        }));

        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        setError(errorMessage);
        setLoading(false);
        throw error;
      }
    },
    []
  );

  /**
   * Search and respond in one call
   */
  const searchAndRespond = useCallback(
    async (
      query: string,
      collection: string = "clients",
      limit: number = 5
    ) => {
      setLoading(true);
      setError(null);

      try {
        const result = await aiTrainingService.searchAndRespond(
          query,
          collection,
          limit
        );

        setState((prev) => ({
          ...prev,
          searchResults: result.searchResults,
          aiResponse: result.aiResponse,
          isLoading: false,
        }));

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        setError(errorMessage);
        setLoading(false);
        throw error;
      }
    },
    []
  );

  /**
   * Batch generate embeddings
   */
  const batchGenerateEmbeddings = useCallback(
    async (collection: string = "clients", batchSize: number = 10) => {
      setLoading(true);
      setError(null);

      try {
        const result = await aiTrainingService.batchGenerateEmbeddings({
          collection,
          batchSize,
        });

        setLoading(false);
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        setError(errorMessage);
        setLoading(false);
        throw error;
      }
    },
    []
  );

  /**
   * Train custom model
   */
  const trainCustomModel = useCallback(
    async (trainingData: TrainingDataItem[], modelName?: string) => {
      setLoading(true);
      setError(null);

      try {
        // Validate training data first
        const validation = aiTrainingService.validateTrainingData(trainingData);
        if (!validation.isValid) {
          throw new Error(
            `Invalid training data: ${validation.errors.join(", ")}`
          );
        }

        const result = await aiTrainingService.trainCustomModel(
          trainingData,
          modelName
        );

        setState((prev) => ({
          ...prev,
          trainingJob: result,
          isLoading: false,
        }));

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        setError(errorMessage);
        setLoading(false);
        throw error;
      }
    },
    []
  );

  /**
   * Get system status
   */
  const getSystemStatus = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const status = await aiTrainingService.getSystemStatus();

      setState((prev) => ({
        ...prev,
        systemStatus: status,
        isLoading: false,
      }));

      return status;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(errorMessage);
      setLoading(false);
      throw error;
    }
  }, []);

  /**
   * Get vector index instructions
   */
  const getVectorIndexInstructions = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const instructions = await aiTrainingService.getVectorIndexInstructions();
      setLoading(false);
      return instructions;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      setError(errorMessage);
      setLoading(false);
      throw error;
    }
  }, []);

  /**
   * Clear results
   */
  const clearResults = useCallback(() => {
    setState((prev) => ({
      ...prev,
      searchResults: [],
      aiResponse: null,
      error: null,
    }));
  }, []);

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setState((prev) => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    ...state,

    // Actions
    performVectorSearch,
    generateAIResponse,
    searchAndRespond,
    batchGenerateEmbeddings,
    trainCustomModel,
    getSystemStatus,
    getVectorIndexInstructions,
    clearResults,
    clearError,

    // Helper functions
    createTrainingData: aiTrainingService.createTrainingData,
    validateTrainingData: aiTrainingService.validateTrainingData,
  };
};
