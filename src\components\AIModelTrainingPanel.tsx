import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Badge } from "./ui/badge";
import { Alert, AlertDescription } from "./ui/alert";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { useAITraining } from "../hooks/useAITraining";
import { TrainingDataItem } from "../services/aiTrainingService";

interface SystemStatus {
  status: string;
  embeddings?: {
    clients_with_embeddings: number;
    restaurants_with_embeddings: number;
  };
}
import {
  Loader2,
  AlertCircle,
  CheckCircle,
  Info,
  Play,
  Search,
  Brain,
  Database,
} from "lucide-react";

export const AIModelTrainingPanel: React.FC = () => {
  const {
    isLoading,
    error,
    searchResults,
    aiResponse,
    systemStatus,
    trainingJob,
    performVectorSearch,
    generateAIResponse,
    searchAndRespond,
    batchGenerateEmbeddings,
    trainCustomModel,
    getSystemStatus,
    getVectorIndexInstructions,
    clearResults,
    clearError,
  } = useAITraining();

  // State for different operations
  const [searchQuery, setSearchQuery] = useState("");
  const [searchCollection, setSearchCollection] = useState("clients");
  const [aiQuery, setAiQuery] = useState("");
  const [trainingDataText, setTrainingDataText] = useState("");
  const [modelName, setModelName] = useState("qonai-custom-model");
  const [batchCollection, setBatchCollection] = useState("clients");
  const [batchSize, setBatchSize] = useState(10);
  const [vectorInstructions, setVectorInstructions] = useState<{
    message: string;
    commands: { collection: string; command: string }[];
    note: string;
  } | null>(null);

  // Load system status on mount
  useEffect(() => {
    getSystemStatus();
  }, [getSystemStatus]);

  const handleVectorSearch = async () => {
    if (!searchQuery.trim()) return;
    await performVectorSearch(searchQuery, searchCollection, 10);
  };

  const handleAIResponse = async () => {
    if (!aiQuery.trim()) return;
    await generateAIResponse(aiQuery, searchResults, true);
  };

  const handleSearchAndRespond = async () => {
    if (!aiQuery.trim()) return;
    await searchAndRespond(aiQuery, searchCollection, 5);
  };

  const handleBatchEmbeddings = async () => {
    await batchGenerateEmbeddings(batchCollection, batchSize);
  };

  const handleTrainModel = async () => {
    try {
      // Parse training data from textarea
      const lines = trainingDataText.split("\n").filter((line) => line.trim());
      const trainingData: TrainingDataItem[] = [];

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          if (data.prompt && data.completion) {
            trainingData.push({
              prompt: data.prompt,
              completion: data.completion,
            });
          }
        } catch {
          // Skip invalid lines
        }
      }

      if (trainingData.length === 0) {
        throw new Error(
          "No valid training data found. Please provide JSONL format."
        );
      }

      await trainCustomModel(trainingData, modelName);
    } catch (error) {
      console.error("Training error:", error);
    }
  };

  const handleGetVectorInstructions = async () => {
    const instructions = await getVectorIndexInstructions();
    setVectorInstructions(instructions);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "healthy":
        return <Badge className="bg-green-100 text-green-800">Healthy</Badge>;
      case "error":
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Unknown</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">AI Model Training Panel</h1>
        <Button
          onClick={() => getSystemStatus()}
          disabled={isLoading}
          variant="outline"
          size="sm"
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin mr-2" />
          ) : (
            <Database className="w-4 h-4 mr-2" />
          )}
          Refresh Status
        </Button>
      </div>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2 text-red-600 hover:text-red-800"
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* System Status */}
      {systemStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {" "}
              <div>
                <p className="text-sm font-medium">Overall Status</p>{" "}
                {getStatusBadge(
                  (systemStatus as unknown as SystemStatus)?.status || "unknown"
                )}
              </div>
              {(systemStatus as unknown as SystemStatus)?.embeddings && (
                <>
                  <div>
                    <p className="text-sm font-medium">
                      Clients with Embeddings
                    </p>
                    <p className="text-2xl font-bold text-blue-600">
                      {
                        (systemStatus as unknown as SystemStatus).embeddings
                          ?.clients_with_embeddings
                      }
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">
                      Restaurants with Embeddings
                    </p>
                    <p className="text-2xl font-bold text-green-600">
                      {
                        (systemStatus as unknown as SystemStatus).embeddings
                          ?.restaurants_with_embeddings
                      }
                    </p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="search" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="search">Vector Search</TabsTrigger>
          <TabsTrigger value="ai">AI Response</TabsTrigger>
          <TabsTrigger value="batch">Batch Processing</TabsTrigger>
          <TabsTrigger value="training">Model Training</TabsTrigger>
          <TabsTrigger value="setup">Setup</TabsTrigger>
        </TabsList>

        {/* Vector Search Tab */}
        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="w-5 h-5" />
                Vector Search
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <Input
                    placeholder="Enter search query..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) =>
                      e.key === "Enter" && handleVectorSearch()
                    }
                  />
                </div>
                <div className="flex gap-2">
                  <select
                    value={searchCollection}
                    onChange={(e) => setSearchCollection(e.target.value)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="clients">Clients</option>
                    <option value="restaurants">Restaurants</option>
                  </select>
                  <Button
                    onClick={handleVectorSearch}
                    disabled={isLoading || !searchQuery.trim()}
                  >
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Search className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>

              {searchResults.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-semibold">Search Results:</h3>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {searchResults.map((result) => (
                      <div key={result.id} className="p-3 border rounded-lg">
                        <div className="flex justify-between items-start">
                          <p className="text-sm font-medium">ID: {result.id}</p>
                          <Badge variant="secondary">
                            Similarity: {(result.similarity * 100).toFixed(2)}%
                          </Badge>
                        </div>
                        <pre className="text-xs mt-2 bg-gray-50 p-2 rounded overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Response Tab */}
        <TabsContent value="ai" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="w-5 h-5" />
                AI Response Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                placeholder="Enter your question or query..."
                value={aiQuery}
                onChange={(e) => setAiQuery(e.target.value)}
                rows={3}
              />
              <div className="flex gap-2">
                <Button
                  onClick={handleSearchAndRespond}
                  disabled={isLoading || !aiQuery.trim()}
                >
                  {isLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ) : (
                    <Play className="w-4 h-4 mr-2" />
                  )}
                  Search & Respond
                </Button>
                <Button
                  onClick={handleAIResponse}
                  disabled={isLoading || !aiQuery.trim()}
                  variant="outline"
                >
                  Generate Response Only
                </Button>
                <Button onClick={clearResults} variant="ghost">
                  Clear
                </Button>
              </div>

              {aiResponse && (
                <div className="space-y-2">
                  <h3 className="font-semibold">AI Response:</h3>
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="whitespace-pre-wrap">{aiResponse}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Batch Processing Tab */}
        <TabsContent value="batch" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Batch Embedding Generation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium">Collection:</label>
                  <select
                    value={batchCollection}
                    onChange={(e) => setBatchCollection(e.target.value)}
                    className="w-full px-3 py-2 border rounded-md mt-1"
                  >
                    <option value="clients">Clients</option>
                    <option value="restaurants">Restaurants</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Batch Size:</label>
                  <Input
                    type="number"
                    value={batchSize}
                    onChange={(e) =>
                      setBatchSize(parseInt(e.target.value) || 10)
                    }
                    min={1}
                    max={100}
                    className="mt-1"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={handleBatchEmbeddings}
                    disabled={isLoading}
                    className="w-full"
                  >
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : null}
                    Generate Embeddings
                  </Button>
                </div>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  This will generate embeddings for documents that don't have
                  them yet. Use this to process existing data or newly added
                  documents.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Model Training Tab */}
        <TabsContent value="training" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Custom Model Training</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Model Name:</label>
                <Input
                  value={modelName}
                  onChange={(e) => setModelName(e.target.value)}
                  placeholder="qonai-custom-model"
                  className="mt-1"
                />
              </div>

              <div>
                <label className="text-sm font-medium">
                  Training Data (JSONL format):
                </label>
                <Textarea
                  value={trainingDataText}
                  onChange={(e) => setTrainingDataText(e.target.value)}
                  placeholder={`{"prompt": "What are the best restaurants for Italian food?", "completion": "Based on your preferences, I recommend..."}\n{"prompt": "I'm allergic to nuts, what should I avoid?", "completion": "You should avoid dishes containing..."}`}
                  rows={10}
                  className="mt-1 font-mono text-sm"
                />
              </div>

              <Button
                onClick={handleTrainModel}
                disabled={isLoading || !trainingDataText.trim()}
                className="w-full"
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : (
                  <Brain className="w-4 h-4 mr-2" />
                )}
                Start Training
              </Button>

              {trainingJob && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Training job initiated:{" "}
                    {JSON.stringify(trainingJob, null, 2)}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Setup Tab */}
        <TabsContent value="setup" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Vector Index Setup</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                onClick={handleGetVectorInstructions}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : null}
                Get Index Creation Instructions
              </Button>

              {vectorInstructions && (
                <div className="space-y-4">
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      {vectorInstructions.message}
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-2">
                    <h3 className="font-semibold">Commands to run:</h3>
                    {vectorInstructions.commands.map(
                      (
                        cmd: { collection: string; command: string },
                        index: number
                      ) => (
                        <div key={index} className="p-3 bg-gray-100 rounded-lg">
                          <p className="text-sm font-medium mb-2">
                            For {cmd.collection} collection:
                          </p>
                          <code className="text-xs bg-gray-800 text-green-400 p-2 rounded block overflow-x-auto">
                            {cmd.command}
                          </code>
                        </div>
                      )
                    )}
                  </div>

                  <Alert>
                    <AlertDescription>
                      {vectorInstructions.note}
                    </AlertDescription>
                  </Alert>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
